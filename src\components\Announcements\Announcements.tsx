import React from "react";
import type { AnnouncementsProps } from "../../types";

const Announcements: React.FC<AnnouncementsProps> = ({
  announcements,
  currentAnnouncement,
}) => {
  return (
    <section className="bg-amber-600 text-white py-3 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center">
          <div className="bg-amber-800 px-4 py-2 rounded-lg mr-4 flex-shrink-0">
            <span className="font-semibold text-sm">New Announcement</span>
          </div>
          <div className="flex-1 overflow-hidden">
            <div className="whitespace-nowrap animate-pulse transition-all duration-500">
              <span className="inline-block mr-16 text-sm font-medium">
                {announcements[currentAnnouncement]}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Announcements;
